{"version": 3, "sources": ["../../is-plain-obj/index.js", "../../merge-options/index.js", "../../merge-options/index.mjs", "../../@react-native-async-storage/async-storage/lib/module/AsyncStorage.ts", "../../@react-native-async-storage/async-storage/lib/module/hooks.ts", "../../@react-native-async-storage/async-storage/lib/module/index.ts"], "sourcesContent": ["'use strict';\n\nmodule.exports = value => {\n\tif (Object.prototype.toString.call(value) !== '[object Object]') {\n\t\treturn false;\n\t}\n\n\tconst prototype = Object.getPrototypeOf(value);\n\treturn prototype === null || prototype === Object.prototype;\n};\n", "'use strict';\nconst isOptionObject = require('is-plain-obj');\n\nconst {hasOwnProperty} = Object.prototype;\nconst {propertyIsEnumerable} = Object;\nconst defineProperty = (object, name, value) => Object.defineProperty(object, name, {\n\tvalue,\n\twritable: true,\n\tenumerable: true,\n\tconfigurable: true\n});\n\nconst globalThis = this;\nconst defaultMergeOptions = {\n\tconcatArrays: false,\n\tignoreUndefined: false\n};\n\nconst getEnumerableOwnPropertyKeys = value => {\n\tconst keys = [];\n\n\tfor (const key in value) {\n\t\tif (hasOwnProperty.call(value, key)) {\n\t\t\tkeys.push(key);\n\t\t}\n\t}\n\n\t/* istanbul ignore else  */\n\tif (Object.getOwnPropertySymbols) {\n\t\tconst symbols = Object.getOwnPropertySymbols(value);\n\n\t\tfor (const symbol of symbols) {\n\t\t\tif (propertyIsEnumerable.call(value, symbol)) {\n\t\t\t\tkeys.push(symbol);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn keys;\n};\n\nfunction clone(value) {\n\tif (Array.isArray(value)) {\n\t\treturn cloneArray(value);\n\t}\n\n\tif (isOptionObject(value)) {\n\t\treturn cloneOptionObject(value);\n\t}\n\n\treturn value;\n}\n\nfunction cloneArray(array) {\n\tconst result = array.slice(0, 0);\n\n\tgetEnumerableOwnPropertyKeys(array).forEach(key => {\n\t\tdefineProperty(result, key, clone(array[key]));\n\t});\n\n\treturn result;\n}\n\nfunction cloneOptionObject(object) {\n\tconst result = Object.getPrototypeOf(object) === null ? Object.create(null) : {};\n\n\tgetEnumerableOwnPropertyKeys(object).forEach(key => {\n\t\tdefineProperty(result, key, clone(object[key]));\n\t});\n\n\treturn result;\n}\n\n/**\n * @param {*} merged already cloned\n * @param {*} source something to merge\n * @param {string[]} keys keys to merge\n * @param {Object} config Config Object\n * @returns {*} cloned Object\n */\nconst mergeKeys = (merged, source, keys, config) => {\n\tkeys.forEach(key => {\n\t\tif (typeof source[key] === 'undefined' && config.ignoreUndefined) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Do not recurse into prototype chain of merged\n\t\tif (key in merged && merged[key] !== Object.getPrototypeOf(merged)) {\n\t\t\tdefineProperty(merged, key, merge(merged[key], source[key], config));\n\t\t} else {\n\t\t\tdefineProperty(merged, key, clone(source[key]));\n\t\t}\n\t});\n\n\treturn merged;\n};\n\n/**\n * @param {*} merged already cloned\n * @param {*} source something to merge\n * @param {Object} config Config Object\n * @returns {*} cloned Object\n *\n * see [Array.prototype.concat ( ...arguments )](http://www.ecma-international.org/ecma-262/6.0/#sec-array.prototype.concat)\n */\nconst concatArrays = (merged, source, config) => {\n\tlet result = merged.slice(0, 0);\n\tlet resultIndex = 0;\n\n\t[merged, source].forEach(array => {\n\t\tconst indices = [];\n\n\t\t// `result.concat(array)` with cloning\n\t\tfor (let k = 0; k < array.length; k++) {\n\t\t\tif (!hasOwnProperty.call(array, k)) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tindices.push(String(k));\n\n\t\t\tif (array === merged) {\n\t\t\t\t// Already cloned\n\t\t\t\tdefineProperty(result, resultIndex++, array[k]);\n\t\t\t} else {\n\t\t\t\tdefineProperty(result, resultIndex++, clone(array[k]));\n\t\t\t}\n\t\t}\n\n\t\t// Merge non-index keys\n\t\tresult = mergeKeys(result, array, getEnumerableOwnPropertyKeys(array).filter(key => !indices.includes(key)), config);\n\t});\n\n\treturn result;\n};\n\n/**\n * @param {*} merged already cloned\n * @param {*} source something to merge\n * @param {Object} config Config Object\n * @returns {*} cloned Object\n */\nfunction merge(merged, source, config) {\n\tif (config.concatArrays && Array.isArray(merged) && Array.isArray(source)) {\n\t\treturn concatArrays(merged, source, config);\n\t}\n\n\tif (!isOptionObject(source) || !isOptionObject(merged)) {\n\t\treturn clone(source);\n\t}\n\n\treturn mergeKeys(merged, source, getEnumerableOwnPropertyKeys(source), config);\n}\n\nmodule.exports = function (...options) {\n\tconst config = merge(clone(defaultMergeOptions), (this !== globalThis && this) || {}, defaultMergeOptions);\n\tlet merged = {_: {}};\n\n\tfor (const option of options) {\n\t\tif (option === undefined) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (!isOptionObject(option)) {\n\t\t\tthrow new TypeError('`' + option + '` is not an Option Object');\n\t\t}\n\n\t\tmerged = merge(merged, {_: option}, config);\n\t}\n\n\treturn merged._;\n};\n", "/**\n * Thin ESM wrapper for CJS named exports.\n *\n * Ref: https://redfin.engineering/node-modules-at-war-why-commonjs-and-es-modules-cant-get-along-9617135eeca1\n */\n\nimport mergeOptions from './index.js';\nexport default mergeOptions;\n", "/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport mergeOptions from \"merge-options\";\nimport type {\n  AsyncStorageStatic,\n  MultiCallback,\n  MultiGetCallback,\n} from \"./types\";\n\n// eslint-disable-next-line @typescript-eslint/ban-types\ntype OnMultiResult = Function;\n// eslint-disable-next-line @typescript-eslint/ban-types\ntype OnResult = Function;\n\nconst merge = mergeOptions.bind({\n  concatArrays: true,\n  ignoreUndefined: true,\n});\n\nfunction mergeLocalStorageItem(key: string, value: string) {\n  const oldValue = window.localStorage.getItem(key);\n  if (oldValue) {\n    const oldObject = JSON.parse(oldValue);\n    const newObject = JSON.parse(value);\n    const nextValue = JSON.stringify(merge(oldObject, newObject));\n    window.localStorage.setItem(key, nextValue);\n  } else {\n    window.localStorage.setItem(key, value);\n  }\n}\n\nfunction createPromise<Result, Callback extends OnResult>(\n  getValue: () => Result,\n  callback?: Callback\n): Promise<Result> {\n  return new Promise((resolve, reject) => {\n    try {\n      const value = getValue();\n      callback?.(null, value);\n      resolve(value);\n    } catch (err) {\n      callback?.(err);\n      reject(err);\n    }\n  });\n}\n\nfunction createPromiseAll<\n  ReturnType,\n  Result,\n  ResultProcessor extends OnMultiResult\n>(\n  promises: Promise<Result>[],\n  callback?: MultiCallback | MultiGetCallback,\n  processResult?: ResultProcessor\n): Promise<ReturnType> {\n  return Promise.all(promises).then(\n    (result) => {\n      const value = processResult?.(result) ?? null;\n      callback?.(null, value);\n      return Promise.resolve(value);\n    },\n    (errors) => {\n      callback?.(errors);\n      return Promise.reject(errors);\n    }\n  );\n}\n\nconst AsyncStorage: AsyncStorageStatic = {\n  /**\n   * Fetches `key` value.\n   */\n  getItem: (key, callback) => {\n    return createPromise(() => window.localStorage.getItem(key), callback);\n  },\n\n  /**\n   * Sets `value` for `key`.\n   */\n  setItem: (key, value, callback) => {\n    return createPromise(\n      () => window.localStorage.setItem(key, value),\n      callback\n    );\n  },\n\n  /**\n   * Removes a `key`\n   */\n  removeItem: (key, callback) => {\n    return createPromise(() => window.localStorage.removeItem(key), callback);\n  },\n\n  /**\n   * Merges existing value with input value, assuming they are stringified JSON.\n   */\n  mergeItem: (key, value, callback) => {\n    return createPromise(() => mergeLocalStorageItem(key, value), callback);\n  },\n\n  /**\n   * Erases *all* AsyncStorage for the domain.\n   */\n  clear: (callback) => {\n    return createPromise(() => window.localStorage.clear(), callback);\n  },\n\n  /**\n   * Gets *all* keys known to the app, for all callers, libraries, etc.\n   */\n  getAllKeys: (callback) => {\n    return createPromise(() => {\n      const numberOfKeys = window.localStorage.length;\n      const keys: string[] = [];\n      for (let i = 0; i < numberOfKeys; i += 1) {\n        const key = window.localStorage.key(i) || \"\";\n        keys.push(key);\n      }\n      return keys;\n    }, callback);\n  },\n\n  /**\n   * (stub) Flushes any pending requests using a single batch call to get the data.\n   */\n  flushGetRequests: () => undefined,\n\n  /**\n   * multiGet resolves to an array of key-value pair arrays that matches the\n   * input format of multiSet.\n   *\n   *   multiGet(['k1', 'k2']) -> [['k1', 'val1'], ['k2', 'val2']]\n   */\n  multiGet: (keys, callback) => {\n    const promises = keys.map((key) => AsyncStorage.getItem(key));\n    const processResult = (result: string[]) =>\n      result.map((value, i) => [keys[i], value]);\n    return createPromiseAll(promises, callback, processResult);\n  },\n\n  /**\n   * Takes an array of key-value array pairs.\n   *   multiSet([['k1', 'val1'], ['k2', 'val2']])\n   */\n  multiSet: (keyValuePairs, callback) => {\n    const promises = keyValuePairs.map((item) =>\n      AsyncStorage.setItem(item[0], item[1])\n    );\n    return createPromiseAll(promises, callback);\n  },\n\n  /**\n   * Delete all the keys in the `keys` array.\n   */\n  multiRemove: (keys, callback) => {\n    const promises = keys.map((key) => AsyncStorage.removeItem(key));\n    return createPromiseAll(promises, callback);\n  },\n\n  /**\n   * Takes an array of key-value array pairs and merges them with existing\n   * values, assuming they are stringified JSON.\n   *\n   *   multiMerge([['k1', 'val1'], ['k2', 'val2']])\n   */\n  multiMerge: (keyValuePairs, callback) => {\n    const promises = keyValuePairs.map((item) =>\n      AsyncStorage.mergeItem(item[0], item[1])\n    );\n    return createPromiseAll(promises, callback);\n  },\n};\n\nexport default AsyncStorage;\n", "import AsyncStorage from \"./AsyncStorage\";\nimport type { AsyncStorageHook } from \"./types\";\n\nexport function useAsyncStorage(key: string): AsyncStorageHook {\n  return {\n    getItem: (...args) => AsyncStorage.getItem(key, ...args),\n    setItem: (...args) => AsyncStorage.setItem(key, ...args),\n    mergeItem: (...args) => AsyncStorage.mergeItem(key, ...args),\n    removeItem: (...args) => AsyncStorage.removeItem(key, ...args),\n  };\n}\n", "import AsyncStorage from \"./AsyncStorage\";\n\nexport { useAsyncStorage } from \"./hooks\";\n\nexport type { AsyncStorageStatic } from \"./types\";\n\nexport default AsyncStorage;\n"], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAEA,WAAO,UAAU,WAAS;AACzB,UAAI,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM,mBAAmB;AAChE,eAAO;AAAA,MACR;AAEA,YAAM,YAAY,OAAO,eAAe,KAAK;AAC7C,aAAO,cAAc,QAAQ,cAAc,OAAO;AAAA,IACnD;AAAA;AAAA;;;ACTA;AAAA;AAAA;AACA,QAAM,iBAAiB;AAEvB,QAAM,EAAC,eAAc,IAAI,OAAO;AAChC,QAAM,EAAC,qBAAoB,IAAI;AAC/B,QAAM,iBAAiB,CAAC,QAAQ,MAAM,UAAU,OAAO,eAAe,QAAQ,MAAM;AAAA,MACnF;AAAA,MACA,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,cAAc;AAAA,IACf,CAAC;AAED,QAAM,aAAa;AACnB,QAAM,sBAAsB;AAAA,MAC3B,cAAc;AAAA,MACd,iBAAiB;AAAA,IAClB;AAEA,QAAM,+BAA+B,WAAS;AAC7C,YAAM,OAAO,CAAC;AAEd,iBAAW,OAAO,OAAO;AACxB,YAAI,eAAe,KAAK,OAAO,GAAG,GAAG;AACpC,eAAK,KAAK,GAAG;AAAA,QACd;AAAA,MACD;AAGA,UAAI,OAAO,uBAAuB;AACjC,cAAM,UAAU,OAAO,sBAAsB,KAAK;AAElD,mBAAW,UAAU,SAAS;AAC7B,cAAI,qBAAqB,KAAK,OAAO,MAAM,GAAG;AAC7C,iBAAK,KAAK,MAAM;AAAA,UACjB;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,MAAM,OAAO;AACrB,UAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,eAAO,WAAW,KAAK;AAAA,MACxB;AAEA,UAAI,eAAe,KAAK,GAAG;AAC1B,eAAO,kBAAkB,KAAK;AAAA,MAC/B;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,WAAW,OAAO;AAC1B,YAAM,SAAS,MAAM,MAAM,GAAG,CAAC;AAE/B,mCAA6B,KAAK,EAAE,QAAQ,SAAO;AAClD,uBAAe,QAAQ,KAAK,MAAM,MAAM,GAAG,CAAC,CAAC;AAAA,MAC9C,CAAC;AAED,aAAO;AAAA,IACR;AAEA,aAAS,kBAAkB,QAAQ;AAClC,YAAM,SAAS,OAAO,eAAe,MAAM,MAAM,OAAO,uBAAO,OAAO,IAAI,IAAI,CAAC;AAE/E,mCAA6B,MAAM,EAAE,QAAQ,SAAO;AACnD,uBAAe,QAAQ,KAAK,MAAM,OAAO,GAAG,CAAC,CAAC;AAAA,MAC/C,CAAC;AAED,aAAO;AAAA,IACR;AASA,QAAM,YAAY,CAAC,QAAQ,QAAQ,MAAM,WAAW;AACnD,WAAK,QAAQ,SAAO;AACnB,YAAI,OAAO,OAAO,GAAG,MAAM,eAAe,OAAO,iBAAiB;AACjE;AAAA,QACD;AAGA,YAAI,OAAO,UAAU,OAAO,GAAG,MAAM,OAAO,eAAe,MAAM,GAAG;AACnE,yBAAe,QAAQ,KAAKA,OAAM,OAAO,GAAG,GAAG,OAAO,GAAG,GAAG,MAAM,CAAC;AAAA,QACpE,OAAO;AACN,yBAAe,QAAQ,KAAK,MAAM,OAAO,GAAG,CAAC,CAAC;AAAA,QAC/C;AAAA,MACD,CAAC;AAED,aAAO;AAAA,IACR;AAUA,QAAM,eAAe,CAAC,QAAQ,QAAQ,WAAW;AAChD,UAAI,SAAS,OAAO,MAAM,GAAG,CAAC;AAC9B,UAAI,cAAc;AAElB,OAAC,QAAQ,MAAM,EAAE,QAAQ,WAAS;AACjC,cAAM,UAAU,CAAC;AAGjB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,cAAI,CAAC,eAAe,KAAK,OAAO,CAAC,GAAG;AACnC;AAAA,UACD;AAEA,kBAAQ,KAAK,OAAO,CAAC,CAAC;AAEtB,cAAI,UAAU,QAAQ;AAErB,2BAAe,QAAQ,eAAe,MAAM,CAAC,CAAC;AAAA,UAC/C,OAAO;AACN,2BAAe,QAAQ,eAAe,MAAM,MAAM,CAAC,CAAC,CAAC;AAAA,UACtD;AAAA,QACD;AAGA,iBAAS,UAAU,QAAQ,OAAO,6BAA6B,KAAK,EAAE,OAAO,SAAO,CAAC,QAAQ,SAAS,GAAG,CAAC,GAAG,MAAM;AAAA,MACpH,CAAC;AAED,aAAO;AAAA,IACR;AAQA,aAASA,OAAM,QAAQ,QAAQ,QAAQ;AACtC,UAAI,OAAO,gBAAgB,MAAM,QAAQ,MAAM,KAAK,MAAM,QAAQ,MAAM,GAAG;AAC1E,eAAO,aAAa,QAAQ,QAAQ,MAAM;AAAA,MAC3C;AAEA,UAAI,CAAC,eAAe,MAAM,KAAK,CAAC,eAAe,MAAM,GAAG;AACvD,eAAO,MAAM,MAAM;AAAA,MACpB;AAEA,aAAO,UAAU,QAAQ,QAAQ,6BAA6B,MAAM,GAAG,MAAM;AAAA,IAC9E;AAEA,WAAO,UAAU,YAAa,SAAS;AACtC,YAAM,SAASA,OAAM,MAAM,mBAAmB,GAAI,SAAS,cAAc,QAAS,CAAC,GAAG,mBAAmB;AACzG,UAAI,SAAS,EAAC,GAAG,CAAC,EAAC;AAEnB,iBAAW,UAAU,SAAS;AAC7B,YAAI,WAAW,QAAW;AACzB;AAAA,QACD;AAEA,YAAI,CAAC,eAAe,MAAM,GAAG;AAC5B,gBAAM,IAAI,UAAU,MAAM,SAAS,2BAA2B;AAAA,QAC/D;AAEA,iBAASA,OAAM,QAAQ,EAAC,GAAG,OAAM,GAAG,MAAM;AAAA,MAC3C;AAEA,aAAO,OAAO;AAAA,IACf;AAAA;AAAA;;;ACpKA,mBAAyB;AACzB,IAAO,wBAAQ,aAAAC;;;ACaf,IAAMC,QAAQC,sBAAaC,KAAK;EAC9BC,cAAc;EACdC,iBAAiB;AACnB,CAAC;AAED,SAASC,sBAAsBC,KAAaC,OAAe;AACzD,QAAMC,WAAWC,OAAOC,aAAaC,QAAQL,GAAG;AAChD,MAAIE,UAAU;AACZ,UAAMI,YAAYC,KAAKC,MAAMN,QAAQ;AACrC,UAAMO,YAAYF,KAAKC,MAAMP,KAAK;AAClC,UAAMS,YAAYH,KAAKI,UAAUjB,MAAMY,WAAWG,SAAS,CAAC;AAC5DN,WAAOC,aAAaQ,QAAQZ,KAAKU,SAAS;EAC5C,OAAO;AACLP,WAAOC,aAAaQ,QAAQZ,KAAKC,KAAK;EACxC;AACF;AAEA,SAASY,cACPC,UACAC,UACiB;AACjB,SAAO,IAAIC,QAAQ,CAACC,SAASC,WAAW;AACtC,QAAI;AACF,YAAMjB,QAAQa,SAAS;AACvBC,mBAAQ,QAARA,aAAQ,UAARA,SAAW,MAAMd,KAAK;AACtBgB,cAAQhB,KAAK;IACf,SAASkB,KAAK;AACZJ,mBAAQ,QAARA,aAAQ,UAARA,SAAWI,GAAG;AACdD,aAAOC,GAAG;IACZ;EACF,CAAC;AACH;AAEA,SAASC,iBAKPC,UACAN,UACAO,eACqB;AACrB,SAAON,QAAQO,IAAIF,QAAQ,EAAEG,KAC1BC,YAAW;AACV,UAAMxB,SAAQqB,kBAAa,QAAbA,kBAAa,SAAA,SAAbA,cAAgBG,MAAM,MAAK;AACzCV,iBAAQ,QAARA,aAAQ,UAARA,SAAW,MAAMd,KAAK;AACtB,WAAOe,QAAQC,QAAQhB,KAAK;EAC9B,GACCyB,YAAW;AACVX,iBAAQ,QAARA,aAAQ,UAARA,SAAWW,MAAM;AACjB,WAAOV,QAAQE,OAAOQ,MAAM;EAC9B,CACF;AACF;AAEA,IAAMC,eAAmC;;;;EAIvCtB,SAASA,CAACL,KAAKe,aAAa;AAC1B,WAAOF,cAAc,MAAMV,OAAOC,aAAaC,QAAQL,GAAG,GAAGe,QAAQ;EACvE;;;;EAKAH,SAASA,CAACZ,KAAKC,OAAOc,aAAa;AACjC,WAAOF,cACL,MAAMV,OAAOC,aAAaQ,QAAQZ,KAAKC,KAAK,GAC5Cc,QACF;EACF;;;;EAKAa,YAAYA,CAAC5B,KAAKe,aAAa;AAC7B,WAAOF,cAAc,MAAMV,OAAOC,aAAawB,WAAW5B,GAAG,GAAGe,QAAQ;EAC1E;;;;EAKAc,WAAWA,CAAC7B,KAAKC,OAAOc,aAAa;AACnC,WAAOF,cAAc,MAAMd,sBAAsBC,KAAKC,KAAK,GAAGc,QAAQ;EACxE;;;;EAKAe,OAAQf,cAAa;AACnB,WAAOF,cAAc,MAAMV,OAAOC,aAAa0B,MAAM,GAAGf,QAAQ;EAClE;;;;EAKAgB,YAAahB,cAAa;AACxB,WAAOF,cAAc,MAAM;AACzB,YAAMmB,eAAe7B,OAAOC,aAAa6B;AACzC,YAAMC,OAAiB,CAAA;AACvB,eAASC,IAAI,GAAGA,IAAIH,cAAcG,KAAK,GAAG;AACxC,cAAMnC,MAAMG,OAAOC,aAAaJ,IAAImC,CAAC,KAAK;AAC1CD,aAAKE,KAAKpC,GAAG;MACf;AACA,aAAOkC;IACT,GAAGnB,QAAQ;EACb;;;;EAKAsB,kBAAkBA,MAAMC;;;;;;;EAQxBC,UAAUA,CAACL,MAAMnB,aAAa;AAC5B,UAAMM,WAAWa,KAAKM,IAAKxC,SAAQ2B,aAAatB,QAAQL,GAAG,CAAC;AAC5D,UAAMsB,gBAAiBG,YACrBA,OAAOe,IAAI,CAACvC,OAAOkC,MAAM,CAACD,KAAKC,CAAC,GAAGlC,KAAK,CAAC;AAC3C,WAAOmB,iBAAiBC,UAAUN,UAAUO,aAAa;EAC3D;;;;;EAMAmB,UAAUA,CAACC,eAAe3B,aAAa;AACrC,UAAMM,WAAWqB,cAAcF,IAAKG,UAClChB,aAAaf,QAAQ+B,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,CACvC;AACA,WAAOvB,iBAAiBC,UAAUN,QAAQ;EAC5C;;;;EAKA6B,aAAaA,CAACV,MAAMnB,aAAa;AAC/B,UAAMM,WAAWa,KAAKM,IAAKxC,SAAQ2B,aAAaC,WAAW5B,GAAG,CAAC;AAC/D,WAAOoB,iBAAiBC,UAAUN,QAAQ;EAC5C;;;;;;;EAQA8B,YAAYA,CAACH,eAAe3B,aAAa;AACvC,UAAMM,WAAWqB,cAAcF,IAAKG,UAClChB,aAAaE,UAAUc,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,CACzC;AACA,WAAOvB,iBAAiBC,UAAUN,QAAQ;EAC5C;AACF;AAEA,IAAA,uBAAeY;;;ACjLR,SAASmB,gBAAgBC,KAA+B;AAC7D,SAAO;IACLC,SAASA,IAAIC,SAASC,qBAAaF,QAAQD,KAAK,GAAGE,IAAI;IACvDE,SAASA,IAAIF,SAASC,qBAAaC,QAAQJ,KAAK,GAAGE,IAAI;IACvDG,WAAWA,IAAIH,SAASC,qBAAaE,UAAUL,KAAK,GAAGE,IAAI;IAC3DI,YAAYA,IAAIJ,SAASC,qBAAaG,WAAWN,KAAK,GAAGE,IAAI;EAC/D;AACF;;;ACJA,IAAA,iBAAeK;", "names": ["merge", "mergeOptions", "merge", "mergeOptions", "bind", "concatArrays", "ignoreUndefined", "mergeLocalStorageItem", "key", "value", "oldValue", "window", "localStorage", "getItem", "oldObject", "JSON", "parse", "newObject", "nextValue", "stringify", "setItem", "createPromise", "getValue", "callback", "Promise", "resolve", "reject", "err", "createPromiseAll", "promises", "processResult", "all", "then", "result", "errors", "AsyncStorage", "removeItem", "mergeItem", "clear", "getAllKeys", "numberOfKeys", "length", "keys", "i", "push", "flushGetRequests", "undefined", "multiGet", "map", "multiSet", "keyValuePairs", "item", "multiRemove", "multiMerge", "useAsyncStorage", "key", "getItem", "args", "AsyncStorage", "setItem", "mergeItem", "removeItem", "AsyncStorage"]}