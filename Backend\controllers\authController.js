// const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const database = require('../services/database')
// const router = express.Router();
require('dotenv').config();


const JWT_SECRET_KEY = process.env.JWT_SECRET_KEY

// Register a new user
// exports.post('/register', async (req, res) => {
exports.register = async (req, res) => {
    const { name,email, password } = req.body;

    try {
        const hashedPassword = await bcrypt.hash(password, 10);

        // Check if user already exists
        const userExists = await database.pool.query('select 1 from users where name=$1',[name]);
        if (userExists.rowCount > 0) {
            return res.status(400).send('User already exists');
        }

        const result = await database.pool.query(
            'INSERT INTO users (name,email, password) VALUES ($1, $2,$3) RETURNING *',
            [name,email, hashedPassword]
        );

        res.status(201).json({
            message: 'User registered successfully!',
            user: result.rows[0],
        });
    } catch (err) {
        res.status(500).json({ error: 'Registration failed!' });

    }
};

// Register admin user
exports.registerAdmin = async (req, res) => {
    const { name, designation, role, userid, password } = req.body;

    try {
        const hashedPassword = await bcrypt.hash(password, 10);

        // Check if admin userid already exists
        const userExists = await database.pool.query('select 1 from admin_users where userid=$1',[userid]);
        if (userExists.rowCount > 0) {
            return res.status(400).json({ error: 'Admin user already exists' });
        }

        const result = await database.pool.query(
            'INSERT INTO admin_users (name, designation, role, userid, password, created_at) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP) RETURNING id, name, designation, role, userid, created_at',
            [name, designation, role, userid, hashedPassword]
        );

        res.status(201).json({
            message: 'Admin user registered successfully!',
            user: result.rows[0],
        });
    } catch (err) {
        console.error('Admin registration error:', err);
        res.status(500).json({ error: 'Admin registration failed!' });
    }
};

// Login a user
// exports.post('/login', async (req, res) => {
exports.login = async (req, res) => {
    const { email, password } = req.body;

    try {
        const result = await database.pool.query('SELECT * FROM users WHERE email = $1', [email]);

        if (result.rows.length === 0) {
            return res.status(400).json({ error: 'User not found!' });
        }

        // console.log('In login API - API Code');

        const user = result.rows[0];
        const isMatch = await bcrypt.compare(password, user.password);

        if (!isMatch) {
            return res.status(400).json({ error: 'Invalid password!' });
        }

        const token = jwt.sign({ name: user.name }, JWT_SECRET_KEY, { expiresIn: '1h' });
        //res.json({ token });

        res.json({
            message: 'Login successful!',
            user: {
                id: user.id,
                name: user.name,
                token : token,
            },
        });
    } catch (err) {
        res.status(500).json({ error: 'Login failed!' });
    }
};

// Login admin user
exports.loginAdmin = async (req, res) => {
    const { userid, password } = req.body;

    try {
        const result = await database.pool.query('SELECT * FROM admin_users WHERE userid = $1', [userid]);

        if (result.rows.length === 0) {
            return res.status(400).json({ error: 'Admin user not found!' });
        }

        const user = result.rows[0];
        const isMatch = await bcrypt.compare(password, user.password);

        if (!isMatch) {
            return res.status(400).json({ error: 'Invalid password!' });
        }

        console.log(isMatch);

        const token = jwt.sign({
            id: user.id,
            userid: user.userid,
            role: user.role
        }, JWT_SECRET_KEY, { expiresIn: '8h' });

         console.log(token);

        res.json({
            message: 'Admin login successful!',
            user: {
                id: user.id,
                name: user.name,
                designation: user.designation,
                role: user.role,
                userid: user.userid,
                token: token,
            },
        });
    } catch (err) {
        console.error('Admin login error:', err);
        res.status(500).json({ error: 'Admin login failed!' });
    }
};

// Reset admin password
exports.resetAdminPassword = async (req, res) => {
    const { userid } = req.body;
    const defaultPassword = 'harekrishna';

    try {
        // Check if admin user exists
        const userExists = await database.pool.query('SELECT 1 FROM admin_users WHERE userid = $1', [userid]);
        if (userExists.rowCount === 0) {
            return res.status(404).json({ error: 'Admin user not found!' });
        }

        const hashedPassword = await bcrypt.hash(defaultPassword, 10);

        await database.pool.query(
            'UPDATE admin_users SET password = $1, updated_at = CURRENT_TIMESTAMP WHERE userid = $2',
            [hashedPassword, userid]
        );

        res.json({
            message: 'Password reset successfully!',
            defaultPassword: defaultPassword
        });
    } catch (err) {
        console.error('Password reset error:', err);
        res.status(500).json({ error: 'Password reset failed!' });
    }
};

// module.exports = router;