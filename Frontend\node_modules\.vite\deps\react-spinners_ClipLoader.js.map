{"version": 3, "sources": ["../../react-spinners/helpers/unitConverter.js", "../../react-spinners/helpers/animation.js", "../../react-spinners/ClipLoader.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseLengthAndUnit = parseLengthAndUnit;\nexports.cssValue = cssValue;\nvar cssUnit = {\n    cm: true,\n    mm: true,\n    in: true,\n    px: true,\n    pt: true,\n    pc: true,\n    em: true,\n    ex: true,\n    ch: true,\n    rem: true,\n    vw: true,\n    vh: true,\n    vmin: true,\n    vmax: true,\n    \"%\": true,\n};\n/**\n * If size is a number, append px to the value as default unit.\n * If size is a string, validate against list of valid units.\n * If unit is valid, return size as is.\n * If unit is invalid, console warn issue, replace with px as the unit.\n *\n * @param {(number | string)} size\n * @return {LengthObject} LengthObject\n */\nfunction parseLengthAndUnit(size) {\n    if (typeof size === \"number\") {\n        return {\n            value: size,\n            unit: \"px\",\n        };\n    }\n    var value;\n    var valueString = (size.match(/^[0-9.]*/) || \"\").toString();\n    if (valueString.includes(\".\")) {\n        value = parseFloat(valueString);\n    }\n    else {\n        value = parseInt(valueString, 10);\n    }\n    var unit = (size.match(/[^0-9]*$/) || \"\").toString();\n    if (cssUnit[unit]) {\n        return {\n            value: value,\n            unit: unit,\n        };\n    }\n    console.warn(\"React Spinners: \".concat(size, \" is not a valid css value. Defaulting to \").concat(value, \"px.\"));\n    return {\n        value: value,\n        unit: \"px\",\n    };\n}\n/**\n * Take value as an input and return valid css value\n *\n * @param {(number | string)} value\n * @return {string} valid css value\n */\nfunction cssValue(value) {\n    var lengthWithunit = parseLengthAndUnit(value);\n    return \"\".concat(lengthWithunit.value).concat(lengthWithunit.unit);\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createAnimation = void 0;\nvar createAnimation = function (loaderName, frames, suffix) {\n    var animationName = \"react-spinners-\".concat(loaderName, \"-\").concat(suffix);\n    if (typeof window == \"undefined\" || !window.document) {\n        return animationName;\n    }\n    var styleEl = document.createElement(\"style\");\n    document.head.appendChild(styleEl);\n    var styleSheet = styleEl.sheet;\n    var keyFrames = \"\\n    @keyframes \".concat(animationName, \" {\\n      \").concat(frames, \"\\n    }\\n  \");\n    if (styleSheet) {\n        styleSheet.insertRule(keyFrames, 0);\n    }\n    return animationName;\n};\nexports.createAnimation = createAnimation;\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar React = __importStar(require(\"react\"));\nvar unitConverter_1 = require(\"./helpers/unitConverter\");\nvar animation_1 = require(\"./helpers/animation\");\nvar clip = (0, animation_1.createAnimation)(\"ClipLoader\", \"0% {transform: rotate(0deg) scale(1)} 50% {transform: rotate(180deg) scale(0.8)} 100% {transform: rotate(360deg) scale(1)}\", \"clip\");\nfunction ClipLoader(_a) {\n    var _b = _a.loading, loading = _b === void 0 ? true : _b, _c = _a.color, color = _c === void 0 ? \"#000000\" : _c, _d = _a.speedMultiplier, speedMultiplier = _d === void 0 ? 1 : _d, _e = _a.cssOverride, cssOverride = _e === void 0 ? {} : _e, _f = _a.size, size = _f === void 0 ? 35 : _f, additionalprops = __rest(_a, [\"loading\", \"color\", \"speedMultiplier\", \"cssOverride\", \"size\"]);\n    var style = __assign({ background: \"transparent !important\", width: (0, unitConverter_1.cssValue)(size), height: (0, unitConverter_1.cssValue)(size), borderRadius: \"100%\", border: \"2px solid\", borderTopColor: color, borderBottomColor: \"transparent\", borderLeftColor: color, borderRightColor: color, display: \"inline-block\", animation: \"\".concat(clip, \" \").concat(0.75 / speedMultiplier, \"s 0s infinite linear\"), animationFillMode: \"both\" }, cssOverride);\n    if (!loading) {\n        return null;\n    }\n    return React.createElement(\"span\", __assign({ style: style }, additionalprops));\n}\nexports.default = ClipLoader;\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,qBAAqB;AAC7B,YAAQ,WAAW;AACnB,QAAI,UAAU;AAAA,MACV,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,KAAK;AAAA,MACL,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,IACT;AAUA,aAAS,mBAAmB,MAAM;AAC9B,UAAI,OAAO,SAAS,UAAU;AAC1B,eAAO;AAAA,UACH,OAAO;AAAA,UACP,MAAM;AAAA,QACV;AAAA,MACJ;AACA,UAAI;AACJ,UAAI,eAAe,KAAK,MAAM,UAAU,KAAK,IAAI,SAAS;AAC1D,UAAI,YAAY,SAAS,GAAG,GAAG;AAC3B,gBAAQ,WAAW,WAAW;AAAA,MAClC,OACK;AACD,gBAAQ,SAAS,aAAa,EAAE;AAAA,MACpC;AACA,UAAI,QAAQ,KAAK,MAAM,UAAU,KAAK,IAAI,SAAS;AACnD,UAAI,QAAQ,IAAI,GAAG;AACf,eAAO;AAAA,UACH;AAAA,UACA;AAAA,QACJ;AAAA,MACJ;AACA,cAAQ,KAAK,mBAAmB,OAAO,MAAM,2CAA2C,EAAE,OAAO,OAAO,KAAK,CAAC;AAC9G,aAAO;AAAA,QACH;AAAA,QACA,MAAM;AAAA,MACV;AAAA,IACJ;AAOA,aAAS,SAAS,OAAO;AACrB,UAAI,iBAAiB,mBAAmB,KAAK;AAC7C,aAAO,GAAG,OAAO,eAAe,KAAK,EAAE,OAAO,eAAe,IAAI;AAAA,IACrE;AAAA;AAAA;;;ACnEA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,kBAAkB;AAC1B,QAAI,kBAAkB,SAAU,YAAY,QAAQ,QAAQ;AACxD,UAAI,gBAAgB,kBAAkB,OAAO,YAAY,GAAG,EAAE,OAAO,MAAM;AAC3E,UAAI,OAAO,UAAU,eAAe,CAAC,OAAO,UAAU;AAClD,eAAO;AAAA,MACX;AACA,UAAI,UAAU,SAAS,cAAc,OAAO;AAC5C,eAAS,KAAK,YAAY,OAAO;AACjC,UAAI,aAAa,QAAQ;AACzB,UAAI,YAAY,oBAAoB,OAAO,eAAe,YAAY,EAAE,OAAO,QAAQ,aAAa;AACpG,UAAI,YAAY;AACZ,mBAAW,WAAW,WAAW,CAAC;AAAA,MACtC;AACA,aAAO;AAAA,IACX;AACA,YAAQ,kBAAkB;AAAA;AAAA;;;ACjB1B;AAAA;AACA,QAAI,WAAY,WAAQ,QAAK,YAAa,WAAY;AAClD,iBAAW,OAAO,UAAU,SAAS,GAAG;AACpC,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,cAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAClB;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AACA,QAAI,kBAAmB,WAAQ,QAAK,oBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAC5F,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AACjF,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAC9D;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACrC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACxB,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACf;AACA,QAAI,qBAAsB,WAAQ,QAAK,uBAAwB,OAAO,SAAU,SAAS,GAAG,GAAG;AAC3F,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACtE,IAAK,SAAS,GAAG,GAAG;AAChB,QAAE,SAAS,IAAI;AAAA,IACnB;AACA,QAAI,eAAgB,WAAQ,QAAK,gBAAiB,SAAU,KAAK;AAC7D,UAAI,OAAO,IAAI,WAAY,QAAO;AAClC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO;AAAM,iBAAS,KAAK,IAAK,KAAI,MAAM,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,iBAAgB,QAAQ,KAAK,CAAC;AAAA;AACvI,yBAAmB,QAAQ,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,QAAI,SAAU,WAAQ,QAAK,UAAW,SAAU,GAAG,GAAG;AAClD,UAAI,IAAI,CAAC;AACT,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,UAAE,CAAC,IAAI,EAAE,CAAC;AACd,UAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,iBAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,cAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,cAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,QACxB;AACJ,aAAO;AAAA,IACX;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,QAAI,QAAQ,aAAa,eAAgB;AACzC,QAAI,kBAAkB;AACtB,QAAI,cAAc;AAClB,QAAI,QAAQ,GAAG,YAAY,iBAAiB,cAAc,8HAA8H,MAAM;AAC9L,aAAS,WAAW,IAAI;AACpB,UAAI,KAAK,GAAG,SAAS,UAAU,OAAO,SAAS,OAAO,IAAI,KAAK,GAAG,OAAO,QAAQ,OAAO,SAAS,YAAY,IAAI,KAAK,GAAG,iBAAiB,kBAAkB,OAAO,SAAS,IAAI,IAAI,KAAK,GAAG,aAAa,cAAc,OAAO,SAAS,CAAC,IAAI,IAAI,KAAK,GAAG,MAAM,OAAO,OAAO,SAAS,KAAK,IAAI,kBAAkB,OAAO,IAAI,CAAC,WAAW,SAAS,mBAAmB,eAAe,MAAM,CAAC;AACzX,UAAI,QAAQ,SAAS,EAAE,YAAY,0BAA0B,QAAQ,GAAG,gBAAgB,UAAU,IAAI,GAAG,SAAS,GAAG,gBAAgB,UAAU,IAAI,GAAG,cAAc,QAAQ,QAAQ,aAAa,gBAAgB,OAAO,mBAAmB,eAAe,iBAAiB,OAAO,kBAAkB,OAAO,SAAS,gBAAgB,WAAW,GAAG,OAAO,MAAM,GAAG,EAAE,OAAO,OAAO,iBAAiB,sBAAsB,GAAG,mBAAmB,OAAO,GAAG,WAAW;AACpc,UAAI,CAAC,SAAS;AACV,eAAO;AAAA,MACX;AACA,aAAO,MAAM,cAAc,QAAQ,SAAS,EAAE,MAAa,GAAG,eAAe,CAAC;AAAA,IAClF;AACA,YAAQ,UAAU;AAAA;AAAA;", "names": []}