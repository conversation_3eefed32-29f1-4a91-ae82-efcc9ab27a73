/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

#import "RCTModulesConformingToProtocolsProvider.h"

@implementation RCTModulesConformingToProtocolsProvider

+(NSArray<NSString *> *)imageURLLoaderClassNames
{
  return @[
    {imageURLLoaderClassNames}
  ];
}

+(NSArray<NSString *> *)imageDataDecoderClassNames
{
  return @[
    {imageDataDecoderClassNames}
  ];
}

+(NSArray<NSString *> *)URLRequestHandlerClassNames
{
  return @[
    {requestHandlersClassNames}
  ];
}

@end
