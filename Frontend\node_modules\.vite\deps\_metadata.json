{"hash": "1253d27e", "configHash": "fc4ac38d", "lockfileHash": "f78fa0d4", "browserHash": "0e1eede1", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "118d1dee", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "7aa1e508", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "3d5c8930", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "4e7dcafe", "needsInterop": true}, "@react-native-async-storage/async-storage": {"src": "../../@react-native-async-storage/async-storage/lib/module/index.js", "file": "@react-native-async-storage_async-storage.js", "fileHash": "819d678f", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "0d1d7ccc", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "90cce8a1", "needsInterop": false}, "date-fns": {"src": "../../date-fns/index.js", "file": "date-fns.js", "fileHash": "4b434759", "needsInterop": false}, "date-fns/locale/en-US": {"src": "../../date-fns/locale/en-US.js", "file": "date-fns_locale_en-US.js", "fileHash": "4f12cc7f", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "723ed9c0", "needsInterop": false}, "react-big-calendar": {"src": "../../react-big-calendar/dist/react-big-calendar.esm.js", "file": "react-big-calendar.js", "fileHash": "7fed36f0", "needsInterop": false}, "react-custom-scrollbars-2": {"src": "../../react-custom-scrollbars-2/lib/index.js", "file": "react-custom-scrollbars-2.js", "fileHash": "eccdea05", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "2262d6b1", "needsInterop": true}, "react-icons/ai": {"src": "../../react-icons/ai/index.mjs", "file": "react-icons_ai.js", "fileHash": "99112c6e", "needsInterop": false}, "react-icons/fi": {"src": "../../react-icons/fi/index.mjs", "file": "react-icons_fi.js", "fileHash": "39018b27", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "ba896821", "needsInterop": false}, "react-spinners/ClipLoader": {"src": "../../react-spinners/ClipLoader.js", "file": "react-spinners_ClipLoader.js", "fileHash": "18fdc962", "needsInterop": true}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "a50d3f29", "needsInterop": false}, "styled-components": {"src": "../../styled-components/dist/styled-components.browser.esm.js", "file": "styled-components.js", "fileHash": "9b53ff24", "needsInterop": false}, "swiper/modules": {"src": "../../swiper/modules/index.mjs", "file": "swiper_modules.js", "fileHash": "7a36ff2d", "needsInterop": false}, "swiper/react": {"src": "../../swiper/swiper-react.mjs", "file": "swiper_react.js", "fileHash": "16d90e38", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "f5d8de8b", "needsInterop": false}}, "chunks": {"chunk-CXZHJKVX": {"file": "chunk-CXZHJKVX.js"}, "chunk-FHWRLMHA": {"file": "chunk-FHWRLMHA.js"}, "chunk-KUR4C64Q": {"file": "chunk-KUR4C64Q.js"}, "chunk-K773IDTU": {"file": "chunk-K773IDTU.js"}, "chunk-KDCVS43I": {"file": "chunk-KDCVS43I.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-IEEFXWTP": {"file": "chunk-IEEFXWTP.js"}, "chunk-RLJ2RCJQ": {"file": "chunk-RLJ2RCJQ.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}